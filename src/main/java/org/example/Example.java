package org.example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class Example {

    public Example() {}

    public String getValue() {
        return "1234";
    }
    
    public List<Integer> collTest() {
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        list.add(4);
        
        return list.stream().filter(i -> i <= 2).collect(Collectors.toList());
    }
}
