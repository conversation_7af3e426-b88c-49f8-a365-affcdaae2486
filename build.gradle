plugins {
    id 'java'
    id 'idea'
}

group = 'org.example'
version = '1.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    //testImplementation(platform('org.junit:junit-bom:5.13.4'))
    testImplementation('org.junit.jupiter:junit-jupiter:5.13.4')
    testImplementation('org.junit.jupiter:junit-jupiter-api:5.13.4')
    testImplementation('org.junit.jupiter:junit-jupiter-engine:5.13.4')
    testRuntimeOnly('org.junit.platform:junit-platform-launcher')
}

test {
    useJUnitPlatform()
}
